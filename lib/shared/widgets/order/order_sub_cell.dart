import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:r_dotted_line_border/r_dotted_line_border.dart';
import 'package:wd/core/models/entities/order_channel_list_entity.dart';
import 'package:wd/core/utils/clipboardTool.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';

class GSOrderSubViewModel {
  String title;
  String id;
  String dateStr;
  String orderNo; // 游戏返水金额
  double betAmount; // 下单金额
  double winAmount; // 盈亏金额

  GSOrderSubViewModel({
    required this.title,
    required this.id,
    required this.dateStr,
    required this.orderNo,
    required this.betAmount,
    required this.winAmount,
  });

  factory GSOrderSubViewModel.formOrderChannelListPageRecord(OrderChannelListPageRecords model) {
    return GSOrderSubViewModel(
        title: model.gameName,
        id: model.thirdPlatformId.toString(),
        dateStr: model.startTime,
        orderNo: model.channelUniqueId,
        betAmount: model.betAmount,
        winAmount: model.winAmount);
  }
}

class GSOrderSubListCell extends StatelessWidget {
  final VoidCallback? onPressed;
  GSOrderSubViewModel model;

  GSOrderSubListCell({super.key, required this.model, this.onPressed});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 8.gw),
      decoration: BoxDecoration(
        color: const Color(0xFF101010),
        borderRadius: BorderRadius.circular(12.gw),
      ),
      child: Column(
        children: [
          // Header section with game name and date
          _buildHeaderSection(context),
          // Information rows
          _buildInfoSection(context),
          // Result section
          _buildResultSection(context),
        ],
      ),
    );
  }

  /// Build header section with game name and date badge
  Widget _buildHeaderSection(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF212121),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.gw),
          topRight: Radius.circular(12.gw),
        ),
      ),
      padding: EdgeInsets.all(16.gw),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            model.title,
            style: TextStyle(
              color: Colors.white,
              fontSize: 20.gw,
              fontWeight: FontWeight.w500,
              fontFamily: 'AnekDevanagari',
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8.gw, vertical: 6.gw),
            decoration: BoxDecoration(
              color: const Color(0xFF161616),
              borderRadius: BorderRadius.circular(6.gw),
            ),
            child: Text(
              model.dateStr,
              style: TextStyle(
                color: const Color(0xFFB4B3B3),
                fontSize: 14.gw,
                fontFamily: 'AnekDevanagari',
              ),
            ),
          ),
        ],
      ),
    );
  }

  _getOrderNoWidget(context) {
    return Container(
        padding: EdgeInsets.symmetric(horizontal: 15.gw, vertical: 10.gw),
        constraints: BoxConstraints(minHeight: 35.h),
        decoration: BoxDecoration(
          border: RDottedLineBorder(
            top: BorderSide(color: Theme.of(context).dividerColor, width: 1),
            bottom: BorderSide(color: Theme.of(context).dividerColor, width: 1),
            dottedLength: 5,
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "单号：",
              style: Theme.of(context).textTheme.titleLarge,
            ),
            Container(
                constraints: BoxConstraints(
                  maxWidth: GSScreenUtil().screenWidth - 150,
                ),
                child: AutoSizeText(model.orderNo, style: Theme.of(context).textTheme.titleLarge)),
            const SizedBox(
              width: 5,
            ),
            InkWell(
              onTap: () => ClipboardTool.setData(model.orderNo),
              child: Padding(
                padding: const EdgeInsets.only(top: 2.0),
                child: Image.asset(
                  "assets/images/common/icon_copy_black.png",
                  width: 16.gw,
                  height: 16.gw,
                ),
              ),
            )
            // GSTextImageButton(
            //   text: '单号：${model.orderNo + model.orderNo}',
            //   imageAssets: "assets/images/common/icon_copy_black.png",
            //   position: GSTextImageButtonPosition.right,
            //   onPressed: () {
            //     ClipboardTool.setData(model.orderNo);
            //   },
            // ),
          ],
        ));
  }

  _getBottomWidget(context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 15.gw),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            '投注：${model.betAmount}元',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          Text(
            model.winAmount.formattedMoney,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontFamily: 'WeChatSansStd',
                  fontWeight: FontWeight.w400,
                  color: model.winAmount >= 0 ? const Color(0xff67ac5c) : const Color(0xffe23f3b),
                ),
          ),
        ],
      ),
    );
  }
}
